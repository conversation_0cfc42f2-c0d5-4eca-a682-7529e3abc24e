using System;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using NHibernate;
using NHibernate.Linq;
using NUnit.Framework;
using Beginor.Cube.Data.Entities;
using Beginor.Cube.Data.Repositories;
using System.Threading.Tasks;

namespace Beginor.Cube.Test.Data;

[TestFixture]
public class CubeRepositoryTest : BaseTest<ICubeRepository> {

    [Test]
    public void _01_CanGetSessionFactory() {
        var factory = Target;
        Assert.That(factory, Is.Not.Null);
    }

    [Test]
    public void _02_CanQueryMeasureCatalog() {
        var factory = ServiceProvider.GetRequiredService<ISessionFactory>();
        using var session = factory.OpenSession();
        var catalogs = session.Query<MeasureCatalog>().ToList();
        Assert.That(catalogs, Is.Not.Empty);
    }

    [Test]
    public void _03_CanQueryMeasureCatalogRelation() {
        var factory = ServiceProvider.GetRequiredService<ISessionFactory>();
        using var session = factory.OpenSession();
        var relations = session.Query<MeasureCatalogRelation>().ToList();
        Assert.That(relations, Is.Not.Empty);
    }

    [Test]
    public async Task _04_CanQueryRootCatalog() {
        var catalogs = await Target.QueryRootCatalogsAsync();
        Assert.That(catalogs, Is.Not.Empty);
    }

}
