#!/bin/bash -e

# 公司名称
COMPANY_NAME=Beginor
# 服务端项目名称
PROJ_NAME=Cube
# 部署虚拟目录
CONTEXT_ROOT=cube
# 新的服务端项目前缀（公司名称+项目名称, 特殊符号需要用 \ 进行转义）
SERVER_PREFIX="${COMPANY_NAME}.${PROJ_NAME}"
# 修改 Docker 编译/部署文件
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./docker/build-docker-image.sh
sed -i.bak "s/beginor/$(echo ${COMPANY_NAME} | tr '[:upper:]' '[:lower:]')/g" ./docker/build-docker-image.sh
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./docker/deploy-docker-image.sh
sed -i.bak "s/beginor/$(echo ${COMPANY_NAME} | tr '[:upper:]' '[:lower:]')/g" ./docker/deploy-docker-image.sh
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./docker/Dockerfile
sed -i.bak "s/MiniApi/${PROJ_NAME}/g" ./docker/Dockerfile
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./docker/docker-compose.yml
sed -i.bak "s/beginor/$(echo ${COMPANY_NAME} | tr '[:upper:]' '[:lower:]')/g" ./docker/docker-compose.yml
# 删除备份文件
find . -name '*.bak' -delete
# 提交一下 Docker 文件
git add *
git commit -m "Rename to ${COMPANY_NAME}.${PROJ_NAME}"
# 修改服务端相关文件
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./src/Api/Properties/launchSettings.json
grep Beginor.MiniApi -rl . --include *.cs | xargs sed -i.bak "s/Beginor\.MiniApi/${SERVER_PREFIX}/g"
grep Beginor.MiniApi -rl . --include *.hbm.xml | xargs sed -i.bak "s/Beginor\.MiniApi/${SERVER_PREFIX}/g"
grep Beginor.MiniApi -rl . --include *.config | xargs sed -i.bak "s/Beginor\.MiniApi/${SERVER_PREFIX}/g"
grep Beginor.MiniApi -rl . --include *.csproj | xargs sed -i.bak "s/Beginor\.MiniApi/${SERVER_PREFIX}/g"

sed -i.bak "s/MiniApi/${PROJ_NAME}/g" ./src/Api/config/hibernate.config
sed -i.bak "s/MiniApi/${PROJ_NAME}/g" ./src/Api/config/log.config

sed -i.bak "s/MiniApi/${PROJ_NAME}/g" ./smartcode.yml
sed -i.bak "s/Beginor/${COMPANY_NAME}/g" ./smartcode.yml
sed -i.bak "s/mini-api/${CONTEXT_ROOT}/g" ./test/Api.http
sed -i.bak "s/MiniApi/${PROJ_NAME}/g" ./README.md
# 删除备份文件
find . -name '*.bak' -delete
# 提交一下服务端文件
git add *
git add -f ./.vscode/launch.json
git add -f ./.vscode/tasks.json
git commit --amend -m "Rename to ${COMPANY_NAME}.${PROJ_NAME}"
# 移动文件至新的目录
git mv mini-api.slnx ${PROJ_NAME}.slnx
# 提交一下服务端文件
git add *
git commit --amend -m "Rename to ${COMPANY_NAME}.${PROJ_NAME}"
