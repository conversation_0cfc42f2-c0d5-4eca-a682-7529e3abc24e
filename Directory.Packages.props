<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <NoWarn>$(NoWarn);NU1903</NoWarn>
  </PropertyGroup>
  <!--
  <ItemDefinitionGroup>
    <PackageReference>
      <PrivateAssets>Compile</PrivateAssets>
    </PackageReference>
  </ItemDefinitionGroup>
  -->
  <ItemGroup>
    <PackageVersion Include="Beginor.AppFx.Api" Version="9.0.3" />
    <PackageVersion Include="Beginor.AppFx.Core" Version="9.0.3" />
    <PackageVersion Include="Beginor.AppFx.DependencyInjection" Version="9.0.3" />
    <PackageVersion Include="Beginor.AppFx.Logging.Log4net" Version="9.0.3" />
    <PackageVersion Include="Beginor.AppFx.Repository.Hibernate" Version="9.0.3" />
    <PackageVersion Include="Beginor.AspNetCore.Authentication.Token" Version="9.0.0" />
    <PackageVersion Include="Beginor.AspNetCore.Middlewares.CustomHeader" Version="9.0.0" />
    <PackageVersion Include="Beginor.AspNetCore.Middlewares.GzipStatic" Version="9.0.0" />
    <PackageVersion Include="Beginor.AspNetCore.Middlewares.SpaFailback" Version="9.0.0" />
    <PackageVersion Include="Dapper" Version="2.1.66" />
    <PackageVersion Include="log4net" Version="3.1.0" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="NHibernate" Version="5.5.2" />
    <PackageVersion Include="NHibernate.Extensions.Npgsql" Version="9.0.3" />
    <PackageVersion Include="NHibernate.Mapping.Attributes" Version="5.1.0" />
    <PackageVersion Include="NHibernate.NetCore" Version="9.0.3" />
    <PackageVersion Include="Npgsql" Version="9.0.3" />
    <PackageVersion Include="NUnit" Version="4.3.2" />
    <PackageVersion Include="NUnit3TestAdapter" Version="5.0.0" />
    <PackageVersion Include="NUnit.Analyzers" Version="4.9.2" />
    <PackageVersion Include="coverlet.collector" Version="6.0.4" />
  </ItemGroup>
</Project>