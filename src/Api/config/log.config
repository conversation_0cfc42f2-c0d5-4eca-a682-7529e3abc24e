<?xml version="1.0" encoding="utf-8" ?>
<log4net debug="false">
  <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="log/log" />
    <appendToFile value="true" />
    <rollingStyle value="Date" />
    <datePattern value="_yyyy-MM-dd.\t\x\t" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="1MB" />
    <staticLogFileName value="false"/>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger %message %newline" />
    </layout>
  </appender>
  <appender name="ColoredConsoleAppender" type="log4net.Appender.AnsiColorTerminalAppender">
    <mapping>
      <level value="ERROR" />
      <foreColor value="Red"/>
    </mapping>
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%date [%thread] %-5level %logger %message %newline" />
    </layout>
  </appender>
  <appender name="AdoNetAppender" type="log4net.Appender.AdoNetAppender">
    <bufferSize value="5" />
    <connectionType value="Npgsql.NpgsqlConnection,Npgsql" />
    <connectionString value="server=127.0.0.1;port=5432;database=nhibernate;user id=postgres;password=********;minimum pool size=10;maximum pool size=10;timeout=30;application name=CubeLogger" />
    <commandText value="insert into public.app_logs(created_at, thread, level, logger, message, exception) values (@created_at, @thread, @level, @logger, @message, @exception);" />
    <parameter>
      <parameterName value="@created_at" />
      <dbType value="DateTime" />
      <layout type="log4net.Layout.RawUtcTimeStampLayout" />
    </parameter>
    <parameter>
      <parameterName value="@thread" />
      <dbType value="String" />
      <size value="8" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%thread" />
      </layout>
    </parameter>
    <parameter>
      <parameterName value="@level" />
      <dbType value="String" />
      <size value="16" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%level" />
      </layout>
    </parameter>
    <parameter>
      <parameterName value="@logger" />
      <dbType value="String" />
      <size value="256" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%logger" />
      </layout>
    </parameter>
    <parameter>
      <parameterName value="@message" />
      <dbType value="String" />
      <size value="4096" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%message" />
      </layout>
    </parameter>
    <parameter>
      <parameterName value="@exception" />
      <dbType value="String" />
      <size value="4096" />
      <layout type="log4net.Layout.ExceptionLayout" />
    </parameter>
  </appender>
  <root>
    <level value="ERROR" />
    <!-- 日志等级，可以设置为 ERROR （只查看错误信息）或 INFO （查看调用信息）设置为 INFO 时，日志量很大，建议调试时打开，调试完毕设回 ERROR -->
    <!-- <appender-ref ref="AdoNetAppender" /> -->
    <appender-ref ref="ColoredConsoleAppender" />
  </root>
  <logger name="Microsoft.Hosting.Lifetime">
    <level value="INFO" />
  </logger>
  <logger name="Beginor.Cube.Startup">
    <level value="INFO" />
  </logger>

</log4net>
