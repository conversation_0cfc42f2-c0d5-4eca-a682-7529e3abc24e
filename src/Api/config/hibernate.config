<?xml version="1.0" encoding="UTF-8"?>
<hibernate-configuration xmlns="urn:nhibernate-configuration-2.2">
  <session-factory>
    <property name="connection.connection_string">server=19.15.88.160;port=55432;database=psdqa;user id=uetl;password=**********;minimum pool size=10;maximum pool size=10;timeout=30;application name=Cube</property>
    <property name="connection.driver_class">NHibernate.Extensions.Npgsql.NpgsqlDriver,NHibernate.Extensions.Npgsql</property>
    <property name="dialect">NHibernate.Extensions.Npgsql.NpgsqlDialect,NHibernate.Extensions.Npgsql</property>
    <property name="linqtohql.generatorsregistry">NHibernate.Extensions.Npgsql.LinqToHqlGeneratorsRegistry,NHibernate.Extensions.Npgsql</property>
    <property name="adonet.batch_size">10</property>
    <property name="default_batch_fetch_size">10</property>
  </session-factory>
</hibernate-configuration>
