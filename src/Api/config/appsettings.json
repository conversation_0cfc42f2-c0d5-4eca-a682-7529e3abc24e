{"AllowedHosts": "*", "spaFailback": {"^/web/(?!assets/|dist/).*": "/web/index.html", "^/apps/web/(?!assets/|dist/).*": "/apps/web/index.html", "^/apps/handset/(?!assets/|dist/).*": "/apps/handset/index.html"}, "customHeader": {".*": {"X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block"}, ".(html|htm)$": {"Cache-Control": "no-store, no-cache", "X-Frame-Options": "SAMEORIGIN"}, ".(main|index).(js|css)$": {"Cache-Control": "no-store, no-cache"}}, "kestrel": {"addServerHeader": false, "allowSynchronousIO": true, "limits": {"maxConcurrentConnections": 1000, "maxConcurrentUpgradedConnections": 1000}}}