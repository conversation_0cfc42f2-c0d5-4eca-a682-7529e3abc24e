using System;
using System.ComponentModel.DataAnnotations;
using Beginor.AppFx.Core;

namespace Beginor.Cube.Models;

public class MeasureCatalogModel : Int32IdNameEntity {
    public override string? Name { get => CatalogName; set => CatalogName = value ?? ""; }
    public string CatalogName { get; set; } = string.Empty;
    public string? CatalogDesc { get; set; }
    public DateTime Created { get; set; } = DateTime.Now;
    public DateTime Updated { get; set; } = DateTime.Now;
}
