using System;
using NHibernate.Mapping.Attributes;
using Beginor.AppFx.Core;

namespace Beginor.Cube.Data.Entities;

/// <summary>指标或标签目录关系</summary>
[Class(Schema = "measures", Table = "measure_catalog_relation")]
public class MeasureCatalogRelation {
    /// <summary>指标或标签目录标识</summary>
    [Id(Name = nameof(ParentId), Column = "parent_id", Type = "int", Generator = "assigned")]
    public virtual int ParentId { get; set; }
    /// <summary>指标或标签所属目录标识</summary>
    [Property(Name = nameof(CatalogId), Column = "catalog_id", Type = "int", NotNull = true)]
    public virtual int CatalogId { get; set; }
}
