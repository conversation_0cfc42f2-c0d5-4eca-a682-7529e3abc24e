using System;
using NHibernate.Mapping.Attributes;
using Beginor.AppFx.Core;

namespace Beginor.Cube.Data.Entities;

/// <summary>指标与标签目录</summary>
[Class(Schema = "measures", Table = "measure_catalog")]
public class MeasureCatalog : BaseEntity<int> {
    /// <summary>指标与标签目录标识</summary>
    [Id(Name = nameof(Id), Column = "id", Type = "int", Generator = "trigger-identity")]
    public override int Id { get { return base.Id; } set { base.Id = value; } }
    /// <summary>指标与标签目录名</summary>
    [Property(Name = nameof(CatalogName), Column = "catalog_name", Type = "string", NotNull = true, Length = 100)]
    public virtual string CatalogName { get; set; } = string.Empty;
    /// <summary>指标与标签目录描述</summary>
    [Property(Name = nameof(CatalogDesc), Column = "catalog_desc", Type = "string", NotNull = false, Length = 100)]
    public virtual string? CatalogDesc { get; set; }
    /// <summary>首次创建时间</summary>
    [Property(Name = nameof(Created), Column = "created", Type = "timestamp", NotNull = true)]
    public virtual DateTime Created { get; set; } = DateTime.Now;
    /// <summary>最后更新时间</summary>
    [Property(Name = nameof(Updated), Column = "updated", Type = "timestamp", NotNull = true)]
    public virtual DateTime Updated { get; set; } = DateTime.Now;
}
