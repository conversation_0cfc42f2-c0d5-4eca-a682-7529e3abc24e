using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using NHibernate;
using NHibernate.Linq;
using Beginor.AppFx.Core;
using Beginor.AppFx.Repository.Hibernate;
using Beginor.Cube.Data.Entities;
using Beginor.Cube.Models;


namespace Beginor.Cube.Data.Repositories;

public class CubeRepository(
    ISession session,
    IMapper mapper,
    ILogger<CubeRepository> logger
) : ICubeRepository {

    public async Task<IList<MeasureCatalog>> QueryRootCatalogsAsync() {
        // var query = from cat in session.Query<MeasureCatalog>()
        //     join rel in session.Query<MeasureCatalogRelation>()
        //     on cat.Id equals rel.ParentId into relations
        //     from rel in relations.DefaultIfEmpty()
        //     where rel == null
        //     select cat;
        var query = session.Query<MeasureCatalog>()
        .GroupJoin(
            session.Query<MeasureCatalogRelation>(),
            cat => cat.Id,
            rel => rel.ParentId,
            (cat, relations) => new { cat, relations }
        )
        .SelectMany(
            x => x.relations.DefaultIfEmpty(),
            (x, rel) => new { x.cat, rel }
        )
        .Where(x => x.rel == null)
        .Select(x => x.cat);
        var data = await query.ToListAsync();
        return data;
    }
}
