using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using NHibernate;
using NHibernate.Linq;
using Beginor.AppFx.Core;
using Beginor.AppFx.Repository.Hibernate;
using Beginor.Cube.Data.Entities;
using Beginor.Cube.Models;


namespace Beginor.Cube.Data.Repositories;

public class CubeRepository(
    ISession session,
    IMapper mapper,
    ILogger<CubeRepository> logger
) : ICubeRepository {

    public async Task<IList<MeasureCatalog>> QueryRootCatalogsAsync() {
        var query = session.QueryOver<MeasureCatalog>().Left.JoinQueryOver<MeasureCatalogRelation>(
            
        )
        // var query = session.Query<MeasureCatalog>().LeftJoin(
        //     session.Query<MeasureCatalogRelation>(),
        //     cat => cat.Id,
        //     rel => rel.ParentId,
        //     (cat, rel) => new { cat, rel }
        // ).Where((cat, rel) => rel == null)
        //     .Select((cat, rel) => cat.cat);
        // var data = await query.ToListAsync();
        // return data;
    }
}
